<?php

namespace App\Http\Controllers;

use App\Models\SystemControl;
use App\Models\User;
use App\Models\Wallet;
use App\Models\Subscription;
use App\Services\SubscriptionFlowService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;
use Exception;
use App\Http\Controllers\NotificationController;

class UserController extends Controller
{
    protected $flowService;

    public function __construct(SubscriptionFlowService $flowService)
    {
        $this->flowService = $flowService;
    }

    // To View Login Page
    public function loginView(){
        return view('auth.login');
    }

    // To View Register Page
    public function registerView(Request $request){
        // Check the current flow
        $currentFlow = $this->flowService->getFlow();

        // In Production mode, redirect to plan selection page if no plan is specified
        if ($currentFlow === 'production' && !$request->has('plan')) {
            return redirect()->route('subscription.public-plans');
        }

        $plan = $request->query('plan', 'auteur'); // Default to 'auteur' if no plan specified
        return view('auth.register', ['plan' => $plan]);
    }

    // To Create A New User
    public function registerUser(Request $req){

        $req->validate([
            'fullName'=> 'required|string|max:255',
            'email'=>'required|string|email|max:255|unique:users,email',
            'password'=>'required|string|min:8',
            'zip'=>'required|string|max:20'
        ], [
            'email.unique' => 'This email address is already registered. Please use a different email or login to your account.',
            'fullName.required' => 'Please enter your full name.',
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'password.required' => 'Please enter a password.',
            'password.min' => 'Your password must be at least 8 characters long.'
        ]);

        // Get the selected plan
        $plan = $req->input('plan', 'auteur');

        // Set book and review limits based on plan
        $bookLimit = ($plan === 'auteur_plus') ? 999 : 3; // Unlimited for AUTEUR+, 3 for AUTEUR
        $reviewLimit = ($plan === 'auteur_plus') ? 7 : 3; // 7 per week for AUTEUR+, 3 for AUTEUR

        // Create a new user
        $newUser = new User;
        $newUser->fullName = $req->fullName;
        $newUser->email = $req->email;
        $newUser->password = Hash::make($req->password);
        $newUser->zipCode = $req->zip;
        $newUser->book_limit = $bookLimit;
        $newUser->review_limit_per_week = $reviewLimit;

        // Core Group Feature Logic for new registrations
        $systemControl = SystemControl::first();
        $isCoreGroupActive = $systemControl && $systemControl->core_group_active;
        $coreGroupCan = $systemControl ? $systemControl->core_group_can : 0;
        $coreGroupRegisteredCount = $systemControl ? $systemControl->core_group_registered_count : 0;

        if ($isCoreGroupActive && $coreGroupRegisteredCount < $coreGroupCan) {
            $newUser->is_core_auteur_candidate = true;
            $systemControl->core_group_registered_count++;
            if ($systemControl->core_group_registered_count >= $coreGroupCan) {
                $systemControl->core_group_active = false; // Automatically disable feature
            }
            $systemControl->save();
        }
        $newUser->save();

        // Create a wallet record for the new user and set the initial balance to default
        $wallet = new Wallet;
        $wallet->userId = $newUser->id; // Assuming 'user_id' is the foreign key linking users and wallets

        $signUpBonus = SystemControl::where('key_type', 'SignUpBonus')->first();

        $wallet->currentBalance = $signUpBonus->value;
        $wallet->save();

        // Log in the user automatically
        Auth::login($newUser);

        // Check the current subscription flow
        $currentFlow = $this->flowService->getFlow();

        // In Alpha flow, create a free subscription without Paddle checkout
        if ($currentFlow === 'alpha') {
            // Always create an Alpha subscription for the user in Alpha flow, regardless of the plan parameter
            $this->flowService->createAlphaSubscription($newUser);

            Log::info('Created Alpha subscription for new user', [
                'user_id' => $newUser->id,
                'plan' => 'alpha'
            ]);

            $welcomeMessage = 'Dear Auteur! Please publish your first book to start and add your "Amazon Reviewer Name" in your profile.';
            $coreAuteurMessage = '';

            if ($newUser->is_core_auteur_candidate) {
                $coreAuteurMessage = 'Dear Auteur, to become a Core Auteur with a FREE 1-year Premium membership after Free Alpha ends, please be patient and stay active.* *active means reviewing and featuring at least a couple of books weekly, as far as the current Vault content allows.';
            }

            // Redirect to author page with success message
            return redirect()->route('author')
                ->with('success', 'Registration successful! Your AUTEUR Alpha subscription has been activated.')
                ->with('welcome_message', $welcomeMessage)
                ->with('core_auteur_message', $coreAuteurMessage);
        }

        // For Production flow or AUTEUR+ plan, redirect to Paddle checkout
        if ($currentFlow === 'production' || $plan === 'auteur_plus') {
            // Determine which price ID to use based on the plan
            $priceId = $plan === 'auteur_plus'
                ? config('services.paddle.prices.auteur_plus')
                : config('services.paddle.prices.auteur');

            // Create session variables to store the plan information and welcome message
            session([
                'selected_plan' => $plan,
                'welcome_message' => 'Dear Auteur! Please publish your first book to start and add your "Amazon Reviewer Name" in your profile.'
            ]);

            if ($newUser->is_core_auteur_candidate) {
                session()->flash('core_auteur_message', 'Dear Auteur, to become a Core Auteur with a FREE 1-year Premium membership after Free Alpha ends, please be patient and stay active.* *active means reviewing and featuring at least a couple of books weekly, as far as the current Vault content allows.');
            }

            // Redirect to auto-checkout page to initiate Paddle checkout
            return redirect()->route('subscription.direct-checkout', $plan);
        }

        // Fallback: redirect to author page with flash messages
        $welcomeMessage = 'Dear Auteur! Please publish your first book to start and add your "Amazon Reviewer Name" in your profile.';
        $coreAuteurMessage = '';

        if ($newUser->is_core_auteur_candidate) {
            $coreAuteurMessage = 'Dear Auteur, to become a Core Auteur with a FREE 1-year Premium membership after Free Alpha ends, please be patient and stay active.* *active means reviewing and featuring at least a couple of books weekly, as far as the current Vault content allows.';
        }

        return redirect()->route('author')
            ->with('success', 'Registration successful! Your subscription has been activated successfully!')
            ->with('welcome_message', $welcomeMessage)
            ->with('core_auteur_message', $coreAuteurMessage);
    }


    public function loginUser(Request $request){
        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials)) {
            // Authentication passed...
            $request->session()->regenerate();

            // Get the authenticated user
            $user = Auth::user();

            // Update weekly login count
            $currentWeek = now()->startOfWeek();
            if ($user->last_login_week === null || $user->last_login_week->lt($currentWeek)) {
                $user->weekly_logins = 1;
                $user->last_login_week = $currentWeek;
            } else {
                $user->weekly_logins++;
            }
            // Mark notifications as read after login
            $notificationController = new NotificationController();
            $notificationController->markAsRead(new Request()); // Pass a new Request instance

            Log::info("User logged in and notifications marked as read", ['user_id' => $user->id]);

            // Check if the user should see the production warning message
            if ($this->flowService->shouldShowProductionWarning($user)) {
                // Check if we're in production mode
                if ($this->flowService->isProductionFlow()) {
                    // Production mode message for Alpha users who haven't upgraded
                    $message = "Dear Auteur, we`ve switched to Production mode, as an Early Access (Alpha) User please upgrade Your Subscription at a special price!";

                    Log::info('Showing production mode warning to Alpha user after login', [
                        'user_id' => $user->id,
                        'message' => $message
                    ]);

                    // Redirect with the warning message (yellow styling)
                    return redirect()->intended('/dashboard')->with('warning', $message);
                } else {
                    // Pre-production countdown message
                    $remainingDays = $this->flowService->getProductionCountdownRemainingDays();
                    $message = "Dear Auteur, we are going to switch to Production mode {$remainingDays} and as an Early Access (Alpha) User you will be able to upgrade at a special price!";

                    Log::info('Showing production countdown warning to user after login', [
                        'user_id' => $user->id,
                        'message' => $message,
                        'remaining_days' => $remainingDays
                    ]);

                    // Redirect with the info message (blue styling)
                    return redirect()->intended('/dashboard')->with('info', $message);
                }
            }

            // Redirect to intended URL or dashboard if no intended URL is set
            return redirect()->intended('/dashboard');
        } else {
            return redirect()->route('login')->with('error', 'Invalid credentials');
        }
    }

    public function logout(Request $request)
    {
        // Get the user ID before logging out
        $userId = Auth::id();

        if ($userId) {
            Log::info("User logging out", ['user_id' => $userId]);
        }

        Auth::logout(); // Log the user out

        // Invalidate the session and regenerate the CSRF token
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('home'); // Redirect to the home page instead of login
    }

    /**
     * Redirect the user to the Google authentication page.
     *
     * @return \Illuminate\Http\Response
     */
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Handle Google callback after authentication.
     *
     * @return \Illuminate\Http\Response
     */
    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            // Check if user already exists
            $user = User::where('email', $googleUser->email)->first();

            if (!$user) {
                // Create new user if doesn't exist
                $user = new User;
                $user->fullName = $googleUser->name;
                $user->email = $googleUser->email;
                $user->google_id = $googleUser->id;
                // Generate a random password for the user
                $user->password = Hash::make(Str::random(16));
                $user->zipCode = '00000'; // Default zip code

                // Core Group Feature Logic for new Google registrations
                $systemControl = SystemControl::first();
                $isCoreGroupActive = $systemControl && $systemControl->core_group_active;
                $coreGroupCan = $systemControl ? $systemControl->core_group_can : 0;
                $coreGroupRegisteredCount = $systemControl ? $systemControl->core_group_registered_count : 0;

                if ($isCoreGroupActive && $coreGroupRegisteredCount < $coreGroupCan) {
                    $user->is_core_auteur_candidate = true;
                    $systemControl->core_group_registered_count++;
                    if ($systemControl->core_group_registered_count >= $coreGroupCan) {
                        $systemControl->core_group_active = false; // Automatically disable feature
                    }
                    $systemControl->save();
                }
                $user->save();

                // Create a wallet for the new user
                $wallet = new Wallet;
                $wallet->userId = $user->id;

                $signUpBonus = SystemControl::where('key_type', 'SignUpBonus')->first();
                $wallet->currentBalance = $signUpBonus->value;
                $wallet->save();

                // Log in the user
                Auth::login($user);

                // Check the current subscription flow
                $currentFlow = $this->flowService->getFlow();

                // In Alpha flow, create a free subscription without Paddle checkout
                if ($currentFlow === 'alpha') {
                    // Create an Alpha subscription for the user
                    $this->flowService->createAlphaSubscription($user);

                    Log::info('Created Alpha subscription for new Google user', [
                        'user_id' => $user->id,
                        'plan' => 'alpha'
                    ]);

                    $welcomeMessage = 'Dear Auteur! Please publish your first book to start and add your "Amazon Reviewer Name" in your profile.';
                    $coreAuteurMessage = '';

                    if ($user->is_core_auteur_candidate) {
                        $coreAuteurMessage = 'Dear Auteur, to become a Core Auteur with a FREE 1-year Premium membership after Free Alpha ends, please be patient and stay active.* *active means reviewing and featuring at least a couple of books weekly, as far as the current Vault content allows.';
                    }

                    // Redirect to author page with success message
                    return redirect()->route('author')
                        ->with('success', 'Registration successful! Your AUTEUR Alpha subscription has been activated.')
                        ->with('welcome_message', $welcomeMessage)
                        ->with('core_auteur_message', $coreAuteurMessage);
                }

                // In Production flow, redirect to subscription plans page
                $welcomeMessage = 'Welcome! Please complete your registration by initiating your AUTEUR plan.';
                $coreAuteurMessage = '';

                if ($user->is_core_auteur_candidate) {
                    $coreAuteurMessage = 'Dear Auteur, to become a Core Auteur with a FREE 1-year Premium membership after Free Alpha ends, please be patient and stay active.* *active means reviewing and featuring at least a couple of books weekly, as far as the current Vault content allows.';
                }

                return redirect()->route('subscription.plans')
                    ->with('welcome_message', $welcomeMessage)
                    ->with('core_auteur_message', $coreAuteurMessage);
            } else {
                // Update Google ID if it's not set
                if (!$user->google_id) {
                    $user->google_id = $googleUser->id;
                    $user->save();
                }

                // Login the user
                Auth::login($user);

                Log::info("Google user logged in", ['user_id' => $user->id]);

                return redirect()->intended('/dashboard');
            }

        } catch (Exception $e) {
            return redirect()->route('login')->with('error', 'Google authentication failed: ' . $e->getMessage());
        }
    }
}
