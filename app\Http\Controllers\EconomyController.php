<?php

namespace App\Http\Controllers;

use App\Models\SystemControl;
use App\Services\SubscriptionFlowService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EconomyController extends Controller
{
    protected $flowService;

    public function __construct(SubscriptionFlowService $flowService)
    {
        $this->flowService = $flowService;
    }

    public function returnBookEconomy()
    {
        // Fetch the single SystemControl record. If it doesn't exist, create a new instance with default values.
        $systemControl = SystemControl::firstOrNew([]);

        $assignment = SystemControl::where('key_type', 'assignments')->first();
        $commission = SystemControl::where('key_type', 'commision')->first();
        $turnArounodTime = SystemControl::where('key_type', 'TurnAroundTime')->get();
        $virtualPoints = SystemControl::where('key_type', 'VP')->get();
        $questionPercentageValue = SystemControl::where('key_type', 'questionsPercentage')->get();
        $freeBoosting = SystemControl::where('key_type', 'freeBoosting')->first();
        $freeBoostingPoints = SystemControl::where('key_type', 'freeBoostingPoints')->first();
        $autoManualApprove = SystemControl::where('key_type', 'autoManualApprove')->first();
        $multipleChoiceQuestions = SystemControl::where('key_type', 'multipleChoiceQuestions')->first();
        $TrueFalseQuetions = SystemControl::where('key_type', 'TrueFalseQuetions')->first();
        $KindlePoints = SystemControl::where('key_type', 'KU')->first();
        $initialPoints = SystemControl::where('key_type', 'InitialPoints')->first();
        $SignUpBonus = SystemControl::where('key_type', 'SignUpBonus')->first();
        $minmcqs = SystemControl::where('key_type', 'minmcqs')->first();
        $mintruefalse = SystemControl::where('key_type', 'mintruefalse')->first();
        $ReviewTimeLimit = SystemControl::where('key_type', 'ReviewTimeLimit')->first();
        $NumberOfBooksTakenPerWeek = SystemControl::where('key_type', 'NumberOfBooksTakenPerWeek')->first();
        $overdueCancelHours = SystemControl::where('key_type', 'overdue_cancel_hours')->first();
        $reviewerNameUpdateInterval = SystemControl::where('key_type', 'AmazonReviewerNameUpdateInterval')->first();
        $autocheckStart = SystemControl::where('key_type', 'day_autocheck_starts')->first();
        $autocheckEnd = SystemControl::where('key_type', 'day_autocheck_ends')->first();
        $autocheckRate = SystemControl::where('key_type', 'autocheck_rate')->first();

        // Get subscription flow settings
        $subscriptionFlow = SystemControl::where('key_type', 'subscription_flow')->first();
        $productionSwitchTime = SystemControl::where('key_type', 'production_switch_time')->first();
        $productionCountdownHours = SystemControl::where('key_type', 'production_countdown_hours')->first();

        // CRITICAL FIX: Check if countdown has expired and we're still in Alpha flow
        // If so, switch to Production flow now during a full page load
        $currentFlow = $this->flowService->getCurrentFlow();
        $countdownExpired = $this->flowService->isProductionCountdownExpired();

        if ($countdownExpired && $currentFlow === 'alpha') {
            Log::info('Countdown expired during page load - switching to Production flow');
            $this->flowService->switchToProductionFlow();

            // Refresh the subscription flow value after switching
            $subscriptionFlow = SystemControl::where('key_type', 'subscription_flow')->first();

            // Log the successful switch
            Log::info('Successfully switched to Production flow during page load');
        }

        // Log the current flow state for debugging
        Log::info('Current subscription flow state', [
            'flow_id' => $subscriptionFlow ? $subscriptionFlow->id : 'not found',
            'flow_value' => $subscriptionFlow ? $subscriptionFlow->value : 'not found',
            'flow_string' => $this->flowService->getCurrentFlow(),
            'countdown_hours' => $productionCountdownHours ? $productionCountdownHours->value : 'not found',
            'switch_time' => $productionSwitchTime ? $productionSwitchTime->value : 'not found'
        ]);

        // Calculate remaining time if countdown has started
        $remainingTime = null;
        $countdownStarted = false;
        $remainingSeconds = null;
        $remainingHours = null;
        $remainingMinutes = null;
        $remainingSecondsDisplay = null;

        if ($productionSwitchTime && $productionSwitchTime->value > 0) {
            $countdownStarted = true;
            $remainingSeconds = $this->flowService->getProductionCountdownRemainingSeconds();
            if ($remainingSeconds !== null) {
                $remainingHours = floor($remainingSeconds / 3600);
                $remainingMinutes = floor(($remainingSeconds % 3600) / 60);
                $remainingSecondsDisplay = $remainingSeconds % 60;
                $remainingTime = $remainingHours . ' hours, ' . $remainingMinutes . ' minutes';
            }
        }

        return view('admin.view.Economy', [
            'systemControl' => $systemControl, // Pass the single systemControl object
            'commission' => $commission,
            'assignment' => $assignment,
            'turnArounodTime' => $turnArounodTime,
            'virtualPoints' => $virtualPoints,
            'freeBoosting' => $freeBoosting,
            'freeBoostingPoints' => $freeBoostingPoints,
            'KindlePoints' => $KindlePoints,
            'autoManualApprove' => $autoManualApprove,
            'multipleChoiceQuestions' => $multipleChoiceQuestions,
            'TrueFalseQuetions' => $TrueFalseQuetions,
            'questionPercentageValue' => $questionPercentageValue,
            'initialPoints' => $initialPoints,
            'SignUpBonus' => $SignUpBonus,
            'minmcqs' => $minmcqs,
            'mintruefalse' => $mintruefalse,
            'ReviewTimeLimit' => $ReviewTimeLimit,
            'BooksTakenPerWeek' => $NumberOfBooksTakenPerWeek,
            'overdueCancelHours' => $overdueCancelHours,
            'reviewerNameUpdateInterval' => $reviewerNameUpdateInterval,
            'autocheckStart' => $autocheckStart,
            'autocheckEnd' => $autocheckEnd,
            'autocheckRate' => $autocheckRate,
            'subscriptionFlow' => $subscriptionFlow,
            'productionSwitchTime' => $productionSwitchTime,
            'productionCountdownHours' => $productionCountdownHours,
            'countdownStarted' => $countdownStarted,
            'remainingTime' => $remainingTime,
            'remainingSeconds' => $remainingSeconds,
            'remainingHours' => $remainingHours,
            'remainingMinutes' => $remainingMinutes,
            'remainingSecondsDisplay' => $remainingSecondsDisplay
        ]);
    }

    public function getTurnAroundTime(Request $request)
    {
        if ($request->turnAroundTime) {
            $turnArounodTime = SystemControl::where('key_type', 'TurnAroundTime')->where('key', $request->turnAroundTime)->pluck('value')->first();
            return response()->json($turnArounodTime);
        }

        if ($request->selectVirtualPoints) {
            $turnArounodTime = SystemControl::where('key_type', 'VP')->where('key', $request->selectVirtualPoints)->pluck('value')->first();
            return response()->json($turnArounodTime);
        }
    }

    public function getQuestionPercentage(Request $request)
    {
        if ($request->questionsPer) {
            // return response()->json($request->questionsPer);
            $questionsPer = SystemControl::where('key_type', 'questionsPercentage')->where('key', $request->questionsPer)->pluck('value')->first();
            if ($questionsPer) {
                return response()->json($questionsPer);
            } else {
                return response()->json('0');
            }
        }
    }

    public function updateBookEconomy(Request $request)
    {

        if ($request->input('numberOfAssignments')) {
            $Assignment = SystemControl::where('key_type', 'assignments')->first();
            $Assignment->value = $request->input('numberOfAssignments');
            $Assignment->save();
        }

        if ($request->input('commisionValue')) {
            $TurnAroundTime = SystemControl::where('key_type', 'commision')->first();
            $TurnAroundTime->value = $request->input('commisionValue');
            $TurnAroundTime->save();
        }
        if ($request->input('selectTurnAroundTime')) {
            $TurnAroundTime = SystemControl::where('key_type', 'TurnAroundTime')->where('key', $request->input('selectTurnAroundTime'))->first();
            $TurnAroundTime->value = $request->input('turnAroundTime');
            $TurnAroundTime->save();
        }

        if ($request->input('selectVirtualPoints')) {
            $VirtualPoints = SystemControl::where('key_type', 'VP')->where('key', $request->input('selectVirtualPoints'))->first();
            $VirtualPoints->value = $request->input('selectVirtualPointsValue');
            $VirtualPoints->save();
        }

        if ($request->input('autoManualApprove')) {
            $autoManualApprove = SystemControl::where('key_type', 'autoManualApprove')->first();
            if ($autoManualApprove) {
                $autoManualApprove->value = 0;
                $autoManualApprove->save();

                \Log::info('Manual Rejection Verification Mode updated to ACTIVE (0) - Reviews will require manual verification');
            } else {
                $autoManualApprove = new SystemControl;
                $autoManualApprove->key = 'Approval Type';
                $autoManualApprove->key_type = 'autoManualApprove';
                $autoManualApprove->created_at = now();
                $autoManualApprove->updated_at = now();
                $autoManualApprove->value = 0;
                $autoManualApprove->save();

                \Log::info('Manual Rejection Verification Mode created as ACTIVE (0) - Reviews will require manual verification');
            }
        } else {
            $autoManualApprove = SystemControl::where('key_type', 'autoManualApprove')->first();
            if ($autoManualApprove) {
                $autoManualApprove->value = 1;
                $autoManualApprove->save();

                \Log::info('Manual Rejection Verification Mode updated to INACTIVE (1) - Reviews will be auto-rejected');
            } else {
                $autoManualApprove = new SystemControl;
                $autoManualApprove->key = 'Approval Type';
                $autoManualApprove->key_type = 'autoManualApprove';
                $autoManualApprove->created_at = now();
                $autoManualApprove->updated_at = now();
                $autoManualApprove->value = 1;
                $autoManualApprove->save();

                \Log::info('Manual Rejection Verification Mode created as INACTIVE (1) - Reviews will be auto-rejected');
            }
        }

        if ($request->input('kindleUnlimited')) {
            $kindlePoints = SystemControl::where("key_type", 'KU')->first();
            $kindlePoints->value = $request->input('kindleUnlimited');
            $kindlePoints->save();
        }

        if ($request->input('freeBoosting')) {
            $freeBoosting = SystemControl::where('key_type', 'freeBoosting')->first();
            $freeBoosting->value = 1;
            $freeBoosting->save();
        } else {
            $freeBoosting = SystemControl::where('key_type', 'freeBoosting')->first();
            $freeBoosting->value = 0;
            $freeBoosting->save();
        }

        if ($request->input('multipleChoiceQuestion')) {
            $mulitpleChoiceQuestions = SystemControl::where('key_type', 'multipleChoiceQuestions')->first();
            if ($mulitpleChoiceQuestions) {
                $mulitpleChoiceQuestions->value = $request->input('multipleChoiceQuestion');
                $mulitpleChoiceQuestions->save();
            } else {
                $mulitpleChoiceQuestions = new SystemControl;
                $mulitpleChoiceQuestions->key = 'Multiple Choice Questions';
                $mulitpleChoiceQuestions->key_type = 'multipleChoiceQuestions';
                $mulitpleChoiceQuestions->created_at = now();
                $mulitpleChoiceQuestions->updated_at = now();
                $mulitpleChoiceQuestions->value = $request->input('multipleChoiceQuestion');
                $mulitpleChoiceQuestions->save();
            }
        }

        if ($request->input('TrueFalseQuetions')) {
            $TrueFalseQuetions = SystemControl::where('key_type', 'TrueFalseQuetions')->first();
            if ($TrueFalseQuetions) {
                $TrueFalseQuetions->value = $request->input('TrueFalseQuetions');
                $TrueFalseQuetions->save();
            } else {
                $TrueFalseQuetions = new SystemControl;
                $TrueFalseQuetions->key = 'True And False Questions';
                $TrueFalseQuetions->key_type = 'TrueFalseQuetions';
                $TrueFalseQuetions->created_at = now();
                $TrueFalseQuetions->updated_at = now();
                $TrueFalseQuetions->value = $request->input('TrueFalseQuetions');
                $TrueFalseQuetions->save();
            }
        }

        if ($request->input('questionsPercentage')) {
            $questionsPercentage = SystemControl::where('key_type', 'questionsPercentage')->where('key', $request->input('questionsPercentage'))->first();
            if ($questionsPercentage) {
                $questionsPercentage->value = $request->input('questionPercentageValue');
                $questionsPercentage->save();
            } else {
                $questionsPercentage = new SystemControl;
                $questionsPercentage->key = $request->input('questionsPercentage');
                $questionsPercentage->key_type = 'questionsPercentage';
                $questionsPercentage->created_at = now();
                $questionsPercentage->updated_at = now();
                $questionsPercentage->value = $request->input('questionPercentageValue');
                $questionsPercentage->save();
            }
        }

        if ($request->input('InitialPoints')) {
            $InitialPoints = SystemControl::where('key_type', 'InitialPoints')->first();
            if ($InitialPoints) {
                $InitialPoints->value = $request->input('InitialPoints');
                $InitialPoints->save();
            } else {
                $InitialPoint = new SystemControl;
                $InitialPoint->key = 'Initial Points';
                $InitialPoint->key_type = 'InitialPoints';
                $InitialPoint->created_at = now();
                $InitialPoint->updated_at = now();
                $InitialPoint->value = $request->input('InitialPoints');
                $InitialPoint->save();
            }
        }

        if($request->input('SignUpBonus')){
            $SignUpBonus = SystemControl::where('key_type', 'SignUpBonus')->first();
            if($SignUpBonus){
                $SignUpBonus->value = $request->input('SignUpBonus');
                $SignUpBonus->save();
            }else{
                $SignUpBonus = new SystemControl;
                $SignUpBonus->key = 'Sign Up Bonus';
                $SignUpBonus->key_type = 'SignUpBonus';
                $SignUpBonus->created_at = now();
                $SignUpBonus->updated_at = now();
                $SignUpBonus->value = $request->input('SignUpBonus');
                $SignUpBonus->save();
            }
        }

        if($request->input('minmcqs')){
            $minmcqs = SystemControl::where('key_type', 'minmcqs')->first();
            if($minmcqs){
                $minmcqs->value = $request->input('minmcqs');
                $minmcqs->save();
            }else{
                $minmcqs = new SystemControl;
                $minmcqs->key = 'Min Mcqs Answers';
                $minmcqs->value = $request->input('minmcqs');
                $minmcqs->key_type = 'minmcqs';
                $minmcqs->created_at = now();
                $minmcqs->updated_at = now();
                $minmcqs->save();
            }
        }

        if($request->input('mintruefalse')){
            $mintruefalse = SystemControl::where('key_type', 'mintruefalse')->first();
            if($mintruefalse){
                $mintruefalse->value = $request->input('mintruefalse');
                $mintruefalse->save();
            }else{
                $mintruefalse = new SystemControl;
                $mintruefalse->key = 'Min True False';
                $mintruefalse->value = $request->input('mintruefalse');
                $mintruefalse->key_type = 'mintruefalse';
                $mintruefalse->created_at = now();
                $mintruefalse->updated_at = now();
                $mintruefalse->save();
            }
        }

        if($request->input('overdue_cancel_hours')){
            $overdueCancelHours = SystemControl::where('key_type', 'overdue_cancel_hours')->first();
            if($overdueCancelHours){
                $overdueCancelHours->value = $request->input('overdue_cancel_hours');
                $overdueCancelHours->save();
            }else{
                $overdueCancelHours = new SystemControl;
                $overdueCancelHours->key = 'A Number of Overdue Hours for an Assignment to be Cancelled';
                $overdueCancelHours->value = $request->input('overdue_cancel_hours');
                $overdueCancelHours->key_type = 'overdue_cancel_hours';
                $overdueCancelHours->created_at = now();
                $overdueCancelHours->updated_at = now();
                $overdueCancelHours->save();
            }
        }

        $NumberOfBooksTakenPerWeek = SystemControl::where('key_type', 'NumberOfBooksTakenPerWeek')->first();
        if($NumberOfBooksTakenPerWeek){
            $NumberOfBooksTakenPerWeek->value = (int)$request->input('NumberOfBooksTakenPerWeek');
            $NumberOfBooksTakenPerWeek->save();
        } else {
            $NumberOfBooksTakenPerWeek = new SystemControl;
            $NumberOfBooksTakenPerWeek->key = 'Books Per Week';
            $NumberOfBooksTakenPerWeek->value = $request->input('NumberOfBooksTakenPerWeek');
            $NumberOfBooksTakenPerWeek->key_type = 'NumberOfBooksTakenPerWeek';
            $NumberOfBooksTakenPerWeek->created_at = now();
            $NumberOfBooksTakenPerWeek->updated_at = now();
            $NumberOfBooksTakenPerWeek->save();
        }

        if($request->has('ReviewTimeLimit')){
            $ReviewTimeLimit = SystemControl::where('key_type', 'ReviewTimeLimit')->first();
            if($ReviewTimeLimit){
                $ReviewTimeLimit->value = $request->input('ReviewTimeLimit');
                $ReviewTimeLimit->save();
            }else{
                $ReviewTimeLimit = new SystemControl;
                $ReviewTimeLimit->key ='ReviewTimeLimit';
                $ReviewTimeLimit->value = $request->input('ReviewTimeLimit');
                $ReviewTimeLimit->key_type = 'ReviewTimeLimit';
                $ReviewTimeLimit->created_at = now();
                $ReviewTimeLimit->updated_at = now();
                $ReviewTimeLimit->save();
            }
        }

        if($request->has('AmazonReviewerNameUpdateInterval')){
            $reviewerNameUpdateInterval = SystemControl::where('key_type', 'AmazonReviewerNameUpdateInterval')->first();
            if($reviewerNameUpdateInterval){
                $reviewerNameUpdateInterval->value = $request->input('AmazonReviewerNameUpdateInterval');
                $reviewerNameUpdateInterval->save();
            }else{
                $reviewerNameUpdateInterval = new SystemControl;
                $reviewerNameUpdateInterval->key = 'Days to pass before Amazon Reviewer Name can be updated';
                $reviewerNameUpdateInterval->value = $request->input('AmazonReviewerNameUpdateInterval');
                $reviewerNameUpdateInterval->key_type = 'AmazonReviewerNameUpdateInterval';
                $reviewerNameUpdateInterval->created_at = now();
                $reviewerNameUpdateInterval->updated_at = now();
                $reviewerNameUpdateInterval->save();
            }
        }

        // Handle day_autocheck_starts field
        if($request->has('day_autocheck_starts')){
            $autocheckStart = SystemControl::where('key_type', 'day_autocheck_starts')->first();
            if($autocheckStart){
                $autocheckStart->value = $request->input('day_autocheck_starts');
                $autocheckStart->save();
            }else{
                $autocheckStart = new SystemControl;
                $autocheckStart->key = 'Day Autocheck Starts';
                $autocheckStart->value = $request->input('day_autocheck_starts') !== '' ? $request->input('day_autocheck_starts') : '72';
                $autocheckStart->key_type = 'day_autocheck_starts';
                $autocheckStart->created_at = now();
                $autocheckStart->updated_at = now();
                $autocheckStart->save();
            }
        }

        // Handle day_autocheck_ends field
        if($request->has('day_autocheck_ends')){
            $autocheckEnd = SystemControl::where('key_type', 'day_autocheck_ends')->first();
            if($autocheckEnd){
                $autocheckEnd->value = $request->input('day_autocheck_ends');
                $autocheckEnd->save();
            }else{
                $autocheckEnd = new SystemControl;
                $autocheckEnd->key = 'Day Autocheck Ends';
                $autocheckEnd->value = $request->input('day_autocheck_ends') !== '' ? $request->input('day_autocheck_ends') : '11';
                $autocheckEnd->key_type = 'day_autocheck_ends';
                $autocheckEnd->created_at = now();
                $autocheckEnd->updated_at = now();
                $autocheckEnd->save();
            }
        }

        // Handle autocheck_rate field
        if($request->has('autocheck_rate')){
            $autocheckRate = SystemControl::where('key_type', 'autocheck_rate')->first();
            if($autocheckRate){
                $autocheckRate->value = $request->input('autocheck_rate');
                $autocheckRate->save();
            }else{
                $autocheckRate = new SystemControl;
                $autocheckRate->key = 'Autocheck Script Rate';
                $autocheckRate->value = $request->input('autocheck_rate') !== '' ? $request->input('autocheck_rate') : '24';
                $autocheckRate->key_type = 'autocheck_rate';
                $autocheckRate->created_at = now();
                $autocheckRate->updated_at = now();
                $autocheckRate->save();
            }
        }

        // Handle subscription flow settings
        if ($request->has('switch_to_production')) {
            // Check if we're already in production flow
            $currentFlow = $this->flowService->getCurrentFlow();

            if ($currentFlow === 'production') {
                // Already in production flow, do nothing
                Log::info('Already in Production flow, no change needed');
            } else {
                // Update the production countdown hours regardless of whether we're switching now
                if ($request->has('production_countdown_hours')) {
                    $hours = (int) $request->input('production_countdown_hours');

                    // Update the production_countdown_hours system control
                    $countdownHoursControl = SystemControl::where('key_type', 'production_countdown_hours')->first();
                    if ($countdownHoursControl) {
                        $countdownHoursControl->value = $hours;
                        $countdownHoursControl->save();

                        Log::info('Updated production countdown hours', ['hours' => $hours]);
                    }

                    // Start the countdown if hours are greater than 0
                    if ($hours > 0) {
                        $this->flowService->startProductionCountdown($hours);

                        Log::info('Production countdown started', [
                            'hours' => $hours,
                            'switch_time' => date('Y-m-d H:i:s', time() + ($hours * 3600))
                        ]);

                        return back()->with('success', 'Production countdown started. The system will switch to Production flow in ' . $hours . ' hours.');
                    } else {
                        // Switch immediately if hours is 0
                        $this->flowService->switchToProductionFlow();

                        Log::info('Switched to Production flow immediately');

                        return back()->with('success', 'System switched to Production flow immediately.');
                    }
                } else {
                    // No hours provided, use default (48)
                    $hours = 48;
                    $this->flowService->startProductionCountdown($hours);

                    Log::info('Production countdown started with default hours', [
                        'hours' => $hours,
                        'switch_time' => date('Y-m-d H:i:s', time() + ($hours * 3600))
                    ]);

                    return back()->with('success', 'Production countdown started with default setting. The system will switch to Production flow in ' . $hours . ' hours.');
                }
            }
        }

        // Handle switch back to Alpha flow (for testing purposes)
        if ($request->has('switch_to_alpha')) {
            $this->flowService->switchToAlphaFlow();

            Log::info('Switched back to Alpha flow');

            return back()->with('success', 'System switched back to Alpha flow for testing purposes.');
        }

        return back()->with('success', 'Economy Updated Successfully');
    }

    /**
     * Check the subscription flow status
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkFlowStatus()
    {
        // Get the current flow state
        $currentFlow = $this->flowService->getCurrentFlow();
        $countdownStarted = $this->flowService->isProductionCountdownStarted();
        $countdownExpired = $this->flowService->isProductionCountdownExpired();
        $remainingSeconds = $this->flowService->getProductionCountdownRemainingSeconds();

        // Get the raw system control values for debugging
        $flowControl = SystemControl::where('key_type', 'subscription_flow')->first();
        $switchTimeControl = SystemControl::where('key_type', 'production_switch_time')->first();
        $countdownHoursControl = SystemControl::where('key_type', 'production_countdown_hours')->first();

        // Log the current state for debugging
        Log::info('Flow status check', [
            'flow_value' => $flowControl ? $flowControl->value : 'not found',
            'flow_string' => $currentFlow,
            'countdown_started' => $countdownStarted,
            'countdown_expired' => $countdownExpired,
            'remaining_seconds' => $remainingSeconds,
            'switch_time_value' => $switchTimeControl ? $switchTimeControl->value : 'not found',
            'countdown_hours_value' => $countdownHoursControl ? $countdownHoursControl->value : 'not found'
        ]);

        // CRITICAL FIX: Check if countdown has expired and we're still in Alpha flow
        // But don't automatically switch to Production flow here to avoid reload loops
        $switchedToProduction = false;
        if ($countdownExpired && $currentFlow === 'alpha') {
            // Instead of switching here, just log that it should be switched
            // The actual switch will happen on the next full page load
            Log::info('Countdown expired - Production flow switch is needed but deferred to avoid reload loops');
            $switchedToProduction = true;
        }

        $remainingTime = null;
        $remainingHours = null;
        $remainingMinutes = null;
        $remainingSeconds_display = null;

        if ($remainingSeconds !== null) {
            $remainingHours = floor($remainingSeconds / 3600);
            $remainingMinutes = floor(($remainingSeconds % 3600) / 60);
            $remainingSeconds_display = $remainingSeconds % 60;
            $remainingTime = $remainingHours . ' hours, ' . $remainingMinutes . ' minutes';
        }

        return response()->json([
            'current_flow' => $currentFlow,
            'countdown_started' => $countdownStarted,
            'countdown_expired' => $countdownExpired,
            'remaining_seconds' => $remainingSeconds,
            'remaining_time' => $remainingTime,
            'hours' => $remainingHours,
            'minutes' => $remainingMinutes,
            'seconds' => $remainingSeconds_display,
            'switch_needed' => $switchedToProduction
        ]);
    }
}
