<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\Advertising;
use App\Models\Book;
use App\Models\BookQuiz;
use App\Models\QuizQuestions;
use App\Models\UpdatedBooks;
use App\Models\Category;
use App\Models\Reader;
use App\Models\User;
use App\Models\NewBookModification;
use App\Models\QuizAnswers;
use App\Models\QuizUserRespnose;
use App\Models\Review;
use App\Models\SystemControl;
use App\Models\Wallet;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DashboardController extends Controller
{
    public function returnDashboard()
    {
        $books = Book::get();

        // Get all pending reviews and those requiring manual verification for the dashboard
        $reviews = DB::table('reviews')
            ->join('books', 'reviews.bookId', '=', 'books.id')
            ->join('users', 'reviews.userid', '=', 'users.id')
            ->whereIn('reviews.reviewStatus', [0, 4]) // Include both pending and manual verification reviews
            ->select('reviews.*', 'books.title as bookTitle', 'users.fullName as userName')
            ->orderBy('reviews.created_at', 'desc') // Most recent first
            ->simplePaginate(50);

        return view('admin.view.dashboard', compact('books', 'reviews'));
    }
    public function returnSingleBook($slug)
    {
        $category = Category::get();
        $book = Book::where('slug', $slug)->first();
        $updateBook = UpdatedBooks::where('bookID', $book->id)->first();
        $getOldCategory = Category::where('id', $book->category)->first();

        $quizs = BookQuiz::where('book_id', $book->id)->with('question.answers')->get();

        if ($updateBook) {
            $currentCategory = Category::where('id', $updateBook->category)->first();
            return view('admin.view.edit-reUpload-single-book', compact('book', 'category', 'currentCategory', 'updateBook', 'getOldCategory', 'quizs'));
        } else {
            return view('admin.view.edit-single-book', compact('book', 'category', 'updateBook', 'quizs'));
        }
    }

    public function returnApprovals()
    {
        $books = book::where('approval_status', '0')->orWhere('approval_status', '3')->simplePaginate(50);
        $ModificationRequests = NewBookModification::all();

        return view('admin.view.approval', compact('books', 'ModificationRequests'));
    }

    public function returnAllBooks()
    {
        $books = book::where('approval_status', '1')->simplePaginate(50);
        return view('admin.view.books', compact('books'));
    }

    public function returnReviews()
    {
        // Get all pending reviews (reviewStatus = 0) and reviews requiring manual verification (reviewStatus = 4)
        $reviews = DB::table('reviews')
            ->join('books', 'reviews.bookId', '=', 'books.id')
            ->join('users', 'reviews.userid', '=', 'users.id')
            ->whereIn('reviews.reviewStatus', [0, 4]) // Include both pending and manual verification reviews
            ->select('reviews.*', 'books.title as bookTitle', 'users.fullName as userName')
            ->orderBy('reviews.created_at', 'desc') // Most recent first
            ->simplePaginate(50);

        return view('admin.view.reviews', compact('reviews'));
    }

    public function returnAllReviews()
    {
        $reviews = DB::table('reviews')
            ->join('books', 'reviews.bookId', '=', 'books.id')
            ->join('users', 'reviews.userid', '=', 'users.id')
            ->whereIn('reviews.reviewStatus', [1, 2, 3]) // Only include fully processed reviews (Validated, Partially Validated, or Rejected)
            ->select('reviews.*', 'books.title as bookTitle', 'users.fullName as userName')
            ->orderBy('created_at', 'desc')
            ->simplePaginate(50);

        return view('admin.view.all-reviews', compact('reviews'));
    }

    public function approve(Request $req, $id)
    {
        $req->validate([
            'BasicPoints' => 'required|numeric|min:1'
        ]);

        $default = Book::find($id);
        $UpdatedBooks = UpdatedBooks::where('bookID', $id)->first();

        $updateNapprove = [
            'title'             => $req->input('book-title')         ?? $UpdatedBooks->title,
            'author'            => $req->input('book-author')          ?? $UpdatedBooks->author,
            'category'          => $req->input('book-category')        ?? $UpdatedBooks->category,
            'book_amazon_url'   => $req->input('book-amazon-url')      ?? $UpdatedBooks->book_amazon_url,
            'explicit_content'  => $req->input('explicit-content')     ?? $UpdatedBooks->explicit_content,
            'book_summary'      => $req->input('book-summary')         ?? $UpdatedBooks->book_summary,
            'favorite_excerpts' => $req->input('favorite-excerpts')    ?? $UpdatedBooks->favorite_excerpts,
            'other_information' => $req->input('other_information')    ?? $UpdatedBooks->other_information ?? $default->other_information,
            'front_book_cover'  => $req->input('front-book-cover')     ?? $UpdatedBooks->front_book_cover ?? $default->front_book_cover,
            'book_pdf'          => $req->input('bookPdf')              ?? $default->book_pdf,
            'approval_status'   => 1,
            'BasicPoints'       => $req->input('BasicPoints'),
        ];

        // Update the Book model where the 'id' matches
        Book::where('id', $id)->update($updateNapprove);

        // Handle quiz updates
        $input = $req->all();
        $mcqQuestions = [];

        // First gather all the MCQ questions
        foreach ($input as $key => $value) {
            if (strpos($key, 'questions-') === 0) {
                $currentQuestionId = str_replace('questions-', '', $key);
                $question = QuizQuestions::find($currentQuestionId);
                if ($question) {
                    $question->update(['question' => $value]);
                }
            }

            if (strpos($key, 'RightAnswer-') === 0) {
                $questionId = str_replace('RightAnswer-', '', $key);
                $mcqQuestions[$questionId] = $value; // value is the position of the correct answer (1-based)
            }

            if (strpos($key, 'trueFalse-') === 0) {
                $questionId = str_replace('trueFalse-', '', $key);
                $selectedAnswer = $value;

                if ($selectedAnswer) {
                    // First, set all answers for this question to not correct
                    QuizAnswers::where('question_id', $questionId)->update(['is_correct' => 0]);

                    // Then set the selected answer as correct
                    QuizAnswers::where('question_id', $questionId)
                             ->where('answer', $selectedAnswer)
                             ->update(['is_correct' => 1]);
                }
            }
        }

        // Now process each MCQ question
        foreach ($mcqQuestions as $questionId => $correctPosition) {
            \Log::info('Processing MCQ question', [
                'questionId' => $questionId,
                'correctPosition' => $correctPosition
            ]);

            // First, set all answers for this question to not correct
            QuizAnswers::where('question_id', $questionId)->update(['is_correct' => 0]);

            // Get all answers for this question
            $answers = QuizAnswers::where('question_id', $questionId)->orderBy('id')->get();

            // Update each answer
            $position = 1;
            foreach ($answers as $answer) {
                // Get the answer text from the input
                foreach ($input as $key => $value) {
                    if (strpos($key, "MCQS-{$answer->id}-") === 0) {
                        $answer->answer = $value;
                        break;
                    }
                }

                // Set this answer as correct if it matches the correct position
                if ($position == $correctPosition) {
                    $answer->is_correct = 1;
                    \Log::info('Setting answer as correct', [
                        'answerId' => $answer->id,
                        'position' => $position,
                        'correctPosition' => $correctPosition
                    ]);
                }

                $answer->save();
                $position++;
            }
        }

        if ($UpdatedBooks) {
            $UpdatedBooks->delete();
        }

        $activity = new Activity;
        $activity->userid = $default->publish_by;
        $activity->bookId = $id;
        $activity->reason = "Your Book Has Been Approved";
        $activity->save();

        // Redirect back to the book list or wherever you want
        return redirect()->route('allBooksDashboard')->with('success', 'Book approved successfully');
    }

    public function updateReviewStatus($id)
    {
        $review = Review::findOrFail($id);
        $previousStatus = $review->reviewStatus;
        $book = Book::find($review->bookId);

        if (!$book) {
            return back()->withErrors('Book not found for this review');
        }

        // Get the current user (reviewer) and wallets
        $currentUser = $review->userid;
        $userWallet = Wallet::where('userId', $currentUser)->first();
        $authorWallet = Wallet::where('userId', $book->publish_by)->first();

        if (!$userWallet || !$authorWallet) {
            return back()->withErrors('User or author wallet not found');
        }

        // If review was previously rejected (status 3), we need to reverse that rejection
        if ($previousStatus == 3) {
            // Get the relevant advertising record
            if ($review->type == 'Paid') {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', '>', '0')
                    ->whereNot('bookPrice', 'KU')
                    ->first();
            } elseif ($review->type == 'KDP') {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', 'KU')
                    ->first();
            } else {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', '<=', '0')
                    ->whereNot('bookPrice', 'KU')
                    ->first();
            }

            if (!$advertising) {
                return back()->withErrors('Advertising information not found for this book');
            }

            // Get the points awarded to reader that were taken back during rejection
            $pointsAwarded = $advertising->points;
            $systemPoints = SystemControl::where('key', 'Commision')->value('value');
            $pointsToAdd = $pointsAwarded - $systemPoints;

            // Re-credit points to reader
            $userWallet->currentBalance = $userWallet->currentBalance + $pointsToAdd;
            $userWallet->save();

            // Deduct points from author (who received them during rejection)
            $authorWallet->currentBalance = $authorWallet->currentBalance - $pointsToAdd;
            $authorWallet->save();
        }
        // If the review was previously partially validated (status 2), we need to handle VP bonus
        else if ($previousStatus == 2) {
            // Get the VP bonus that was returned to the author
            if ($review->type == 'Paid') {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', '>', '0')
                    ->whereNot('bookPrice', 'KU')
                    ->first();

                if ($advertising) {
                    $vpBonusPoints = SystemControl::where('key_type', 'VP')
                        ->where('key', $advertising->bookPrice)
                        ->value('value');

                    if ($vpBonusPoints) {
                        // Return VP bonus to reader
                        $userWallet->currentBalance = $userWallet->currentBalance + $vpBonusPoints;
                        $userWallet->save();

                        // Take VP bonus back from author
                        $authorWallet->currentBalance = $authorWallet->currentBalance - $vpBonusPoints;
                        $authorWallet->save();
                    }
                }
            }
        }

        // Update the review status to validated (1)
        $review->update([
            'reviewStatus' => 1,
        ]);

        // Update status to '0' in Reader table
        Reader::where('userId', $currentUser)
            ->where('bookId', $book->id)
            ->update(['status' => 0]);

        // Create activity notifications
        $ReviewPublisher = User::findOrFail($book->publish_by);

        $activity = new Activity;
        $activity->userid = $book->publish_by;
        $activity->bookId = $review->bookId;
        $activity->reason = 'Review has been validated by admin';
        $activity->save();

        $reviwer = new Activity;
        $reviwer->userId = $currentUser;
        $reviwer->bookId = $review->bookId;
        $reviwer->reason = "Your review has been approved";
        $reviwer->save();

        return back()->withSuccess('Review successfully validated');
    }

    public function updatePartialPoints($id)
    {
        $review = Review::findOrFail($id);
        $previousStatus = $review->reviewStatus;
        $book = Book::findOrFail($review->bookId);

        // Get user and author wallets
        $userWallet = Wallet::where('userId', $review->userid)->first();
        $authorWallet = Wallet::where('userId', $book->publish_by)->first();

        if (!$userWallet || !$authorWallet) {
            return back()->withErrors('User or author wallet not found');
        }

        // First, handle any previous validation status
        if ($previousStatus == 1) {
            // If it was fully validated before, no additional balance adjustments needed
            // Full validation doesn't have any VP bonus adjustments compared to partial
        }
        else if ($previousStatus == 3) {
            // If it was rejected before, restore the basic points to the reader
            if ($review->type == 'Paid') {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', '>', '0')
                    ->whereNot('bookPrice', 'KU')
                    ->first();
            } elseif ($review->type == 'KDP') {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', 'KU')
                    ->first();
            } else {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', '<=', '0')
                    ->whereNot('bookPrice', 'KU')
                    ->first();
            }

            if (!$advertising) {
                return back()->withErrors('Advertising information not found');
            }

            // Restore base points to reader (without VP bonus)
            $pointsAwarded = $advertising->points;
            $systemPoints = SystemControl::where('key', 'Commision')->value('value');
            $pointsToRestore = $pointsAwarded - $systemPoints;

            // Return basic points to reader
            $userWallet->currentBalance = $userWallet->currentBalance + $pointsToRestore;
            $userWallet->save();

            // Take back points from author
            $authorWallet->currentBalance = $authorWallet->currentBalance - $pointsToRestore;
            $authorWallet->save();
        }

        // Handle VP bonus for Partial Validation
        if ($review->type == 'Paid') {
            $advertising = Advertising::where('bookId', $book->id)
                ->where('bookPrice', '>', '0')
                ->whereNot('bookPrice', 'KU')
                ->first();

            if (!$advertising) {
                return back()->withErrors('VP advertising record not found');
            }

            $vpBonusPoints = SystemControl::where('key_type', 'VP')
                ->where('key', $advertising->bookPrice)
                ->value('value');

            if (!$vpBonusPoints) {
                $vpBonusPoints = 0;
            }

            // VP points go to author
            $authorWallet->currentBalance = $authorWallet->currentBalance + $vpBonusPoints;
            $authorWallet->save();

            // VP points deducted from reader
            $userWallet->currentBalance = $userWallet->currentBalance - $vpBonusPoints;
            $userWallet->save();

            // Update review status to partially validated (2)
            $review->update([
                'reviewStatus' => 2,
            ]);

            // Create notification activities
            $reviewPublisher = User::findOrFail($book->publish_by);

            // Activity for reader
            $readerActivity = new Activity();
            $readerActivity->userid = $review->userid;
            $readerActivity->bookId = $review->bookId;
            $readerActivity->reason = 'Your review was approved. However, it was not stamped as a Verified Purchase. You had received ' . $vpBonusPoints . ' snaps for the VP option on this review, so we have deducted those snaps from your account.';
            $readerActivity->save();

            // Activity for author
            $authorActivity = new Activity();
            $authorActivity->userid = $book->publish_by;
            $authorActivity->bookId = $review->bookId;
            $authorActivity->reason = 'A review for your book was approved partially (not VP). The ' . $vpBonusPoints . ' VP bonus points have been returned to your account.';
            $authorActivity->save();

            return back()->withSuccess('Review has been partially validated');
        }

        // If not a Paid review, just validate it normally
        $review->update([
            'reviewStatus' => 1,
        ]);

        return back()->withSuccess('Review validated (not a VP review)');
    }

    public function RejectedReviewPoints($id)
    {
        $review = Review::findOrFail($id);
        $previousStatus = $review->reviewStatus;

        if (!$review) {
            return back()->withErrors('Unable to find review');
        }

        $book = Book::findOrFail($review->bookId);
        if (!$book) {
            return back()->withErrors('Unable to find book');
        }

        // Get user and author wallets
        $userWallet = Wallet::where('userId', $review->userid)->first();
        $authorWallet = Wallet::where('userId', $book->publish_by)->first();

        if (!$userWallet || !$authorWallet) {
            return back()->withErrors('User or author wallet not found');
        }

        // If previously validated or partially validated, we need to roll back
        if ($previousStatus == 1 || $previousStatus == 2) {
            // Get the advertising record
            if ($review->type == 'Paid') {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', '>', '0')
                    ->whereNot('bookPrice', 'KU')
                    ->first();
            } elseif ($review->type == 'KDP') {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', 'KU')
                    ->first();
            } else {
                $advertising = Advertising::where('bookId', $book->id)
                    ->where('bookPrice', '<=', '0')
                    ->whereNot('bookPrice', 'KU')
                    ->first();
            }

            if (!$advertising) {
                return back()->withErrors('Advertising record not found');
            }

            // Get points awarded to reader
            $pointsAwarded = $advertising->points;

            // For partially validated reviews with VP bonus
            if ($previousStatus == 2 && $review->type == 'Paid') {
                // We've already handled the VP bonus in the partial validation
                // Now we just need to handle the basic points
            }

            // Get commission
            $systemPoints = SystemControl::where('key', 'Commision')->value('value');
            $pointsToDeduct = $pointsAwarded - $systemPoints;

            // Deduct points from reader
            $userWallet->currentBalance = $userWallet->currentBalance - $pointsToDeduct;
            $userWallet->save();

            // Return points to author
            $authorWallet->currentBalance = $authorWallet->currentBalance + $pointsToDeduct;
            $authorWallet->save();
        }

        // Update review status to rejected (3)
        $review->update([
            'reviewStatus' => 3,
        ]);

        // Create activity notifications
        $reviewPublisher = User::findOrFail($book->publish_by);

        $activity = new Activity;
        $activity->userid = $review->userid;
        $activity->bookId = $review->bookId;
        $activity->reason = 'Your review has been rejected. The tokens you received have been returned to the author.';
        $activity->save();

        $authorActivity = new Activity;
        $authorActivity->userid = $book->publish_by;
        $authorActivity->bookId = $review->bookId;
        $authorActivity->reason = 'A review for your book has been rejected. The tokens have been returned to your account.';
        $authorActivity->save();

        return back()->withSuccess('Review rejected and balances updated');
    }

    public function getUpdatedDashboard($slug)
    {
        $user = Auth::user();
        $category = Category::get();
        $book = Book::where('slug', $slug)->first();
        $Author = true;
        $quizs = BookQuiz::where('book_id', $book->id)->with('question.answers')->get();
        $trueFalseQuestions = SystemControl::where('key_type', 'TrueFalseQuetions')->first();
        $multipleChoiceQuestions = SystemControl::where('key_type', 'multipleChoiceQuestions')->first();

        // Log quiz answers to debug correct answer selection
        foreach ($quizs as $quiz) {
            foreach ($quiz->question as $question) {
                if ($question->questions_type == 'multiple_choice') {
                    $answerInfo = [];
                    foreach ($question->answers as $answer) {
                        $answerInfo[] = [
                            'id' => $answer->id,
                            'answer' => $answer->answer,
                            'is_correct' => $answer->is_correct
                        ];
                    }

                    \Log::info('Author MCQ Question', [
                        'question_id' => $question->id,
                        'question' => $question->question,
                        'answers' => $answerInfo
                    ]);
                }
            }
        }

        return view('author.author-edit-single-book', compact('book', 'category', 'Author', 'quizs', 'trueFalseQuestions', 'multipleChoiceQuestions'));
    }

    public function requestModification(Request $req)
    {
        $updatedBook = UpdatedBooks::find($req->bookId);
        if ($updatedBook) {
            $updatedBook->ModificationText = $req->modificationText;
            $updatedBook->save();
        }

        $book = Book::find($updatedBook->bookID);
        if ($book) {
            $book->update(['approval_status' => '2']);
        }

        $activity = new Activity;
        $activity->userid = $book->publish_by;
        $activity->reason = "Your Book '" . $book->title . "' Required Some Changes";
        $activity->save();

        return response()->json(['success' => 'Modification Requested Successfully', 'book' => $book]);
    }

    public function requestNewModification(Request $req)
    {
        $isRequestAlreadyExists = NewBookModification::where('bookId', $req->bookId)->first();

        if (!$isRequestAlreadyExists) {

            $books = Book::findOrFail($req->bookId);
            $books->approval_status = 2;
            $books->save();

            $mfr = new NewBookModification;
            $mfr->bookId = $req->bookId;
            $mfr->modification = $req->modificationText;
            $mfr->status = true;
            $mfr->save();

        } else {

            $books = Book::findOrFail($req->bookId);
            $books->approval_status = 2;
            $books->save();

            $isRequestAlreadyExists->modification = $req->modificationText;
            $isRequestAlreadyExists->status = true;
            $isRequestAlreadyExists->save();
        }

        $activity = new Activity;
        $activity->userid = $books->publish_by;
        $activity->reason = "Your Book '" . $books->title . "' Required Some Changes";
        $activity->save();

        return response()->json(['success' => 'Your book has been updated successfully'], 200);
    }

    public function UpdateAdminBoook(Request $req, $id)
    {
        $validate = $req->validate([
            'book-title' => 'required|string',
            'book-author' => 'required|string',
            'book-category' => 'required|string',
            'book-amazon-url' => 'required|string',
            'explicit-content' => 'required|string',
            'kindleUnlimited' => 'required|string',
            'basicPoints' => 'required|string',
            'book-summary' => 'required|string',
            'favorite-excerpts' => 'nullable|string',
            'wordCount' => 'required|numeric|min:1',
            'wordCount' => 'required|numeric|min:1',
            'other_information' => 'nullable|string' // Add validation for other_information
        ]);

        $book = Book::where('id', $id)->first();
        $book->title = $validate['book-title'];
        $book->author = $validate['book-author'];
        $book->category = $validate['book-category'];
        $book->book_amazon_url = $validate['book-amazon-url'];
        $book->explicit_content = $validate['explicit-content'];
        $book->book_summary = $validate['book-summary'];
        $book->favorite_excerpts = $validate['favorite-excerpts'];
        $book->wordCount = $validate['wordCount'];
        $book->BasicPoints = $validate['basicPoints'];
        $book->other_information = $validate['other_information']; // Add other_information to update
        $book->save();

        $isExist = BookQuiz::where('book_id', $id)->first();

        if (!$isExist) {
            $trueFalse = [];
            $trueFalseQuestions = SystemControl::where('key_type', 'TrueFalseQuetions')->first();

            if ($trueFalseQuestions) {
                for ($i = 1; $i <= $trueFalseQuestions->value; $i++) {
                    $questionKey = 'trueFalse' . $i;
                    $answerKey = 'answer' . $i;

                    if ($req->has($questionKey) && $req->has($answerKey)) {
                        $trueFalse[] = [
                            'question' => $req->$questionKey,
                            'answer' => $req->$answerKey
                        ];
                    }
                }
            }

            // MCQs For Loop
            $MCSQs = [];
            $multipleChoiceQuestions = SystemControl::where('key_type', 'multipleChoiceQuestions')->first();

            if ($multipleChoiceQuestions) {
                for ($i = 1; $i <= $multipleChoiceQuestions->value; $i++) {
                    $questionKey = 'mcqsQuestion' . $i;
                    $answer1Key = 'MCQS' . $i . '1';
                    $answer2Key = 'MCQS' . $i . '2';
                    $answer3Key = 'MCQS' . $i . '3';
                    $answer4Key = 'MCQS' . $i . '4';
                    $rightAnswerKey = 'rightAnswerMCQS' . $i;

                    if ($req->has($questionKey) && $req->has($answer1Key) && $req->has($answer2Key) && $req->has($answer3Key) && $req->has($answer4Key) && $req->has($rightAnswerKey)) {
                        $MCSQs[] = [
                            'question' => $req->$questionKey,
                            'choices' => [
                                'choice1' => $req->$answer1Key,
                                'choice2' => $req->$answer2Key,
                                'choice3' => $req->$answer3Key,
                                'choice4' => $req->$answer4Key
                            ],
                            'answer' => intval($req->$rightAnswerKey)
                        ];
                    }
                }
            }

            $quiz = BookQuiz::insert([
                'book_id' => $id
            ]);

            $quiz = BookQuiz::where('book_id', $id)->first();

            foreach ($trueFalse as $item) {
                $quizQuestion = new QuizQuestions;
                $quizQuestion->quiz_id = $quiz->id;
                $quizQuestion->question = $item['question'];
                $quizQuestion->questions_type = 'true_false';
                $quizQuestion->save();

                $quizAnswer = new QuizAnswers;
                $quizAnswer->question_id = $quizQuestion->id;
                $quizAnswer->answer = $item['answer'];
                $quizAnswer->is_correct = 1;
                $quizAnswer->save();

                $incorrectAnswer = new QuizAnswers;
                $incorrectAnswer->question_id = $quizQuestion->id;
                $incorrectAnswer->answer = ($item['answer'] == 'True') ? 'False' : 'True';
                $incorrectAnswer->is_correct = 0;
                $incorrectAnswer->save();
            }

            foreach ($MCSQs as $mcq) {
                $quizQuestion = new QuizQuestions;
                $quizQuestion->quiz_id = $quiz->id;
                $quizQuestion->question = $mcq['question'];
                $quizQuestion->questions_type = 'multiple_choice';
                $quizQuestion->save();

                $count = 0;
                foreach ($mcq['choices'] as $index => $choice) {
                    $count++;
                    $quizAnswer = new QuizAnswers;
                    $quizAnswer->question_id = $quizQuestion->id;
                    $quizAnswer->answer = $choice;
                    $quizAnswer->is_correct = $count === $mcq['answer'] ? 1 : 0;
                    ;
                    $quizAnswer->save();
                }
            }
        } else {
            $input = $req->all();

            foreach ($input as $key => $value) {
                if (strpos($key, 'questions-') === 0) {
                    $currentQuestionId = str_replace('questions-', '', $key);
                    $question = QuizQuestions::find($currentQuestionId);
                    if ($question) {
                        $question->update(['question' => $value]);
                    }
                }

                if (strpos($key, 'MCQS-') === 0) {
                    $parts = explode('-', str_replace('MCQS-', '', $key));
                    $answerId = $parts[0];
                    $optionId = $parts[1];
                    $questionId = $parts[2];
                    $isCorrect = $req->input("RightAnswer-$questionId");

                    QuizAnswers::where('question_id', $questionId)->update(['is_correct' => 0]);

                    $quizAnswer = QuizAnswers::where('question_id', $questionId)->get();
                    $count = 0;
                    foreach ($quizAnswer as $answer) {
                        $count++;
                        $thisAnswer = $req->input("MCQS-{$answer->id}-$count-$questionId");

                        $answer->answer = $thisAnswer;
                        $answer->is_correct = ($count == $isCorrect ? 1 : 0);
                        $answer->save();
                    }
                }

                if (strpos($key, 'trueFalse-') === 0) {
                    $trueFalseId = str_replace('trueFalse-', '', $key);
                    $questionId = (int) $trueFalseId;
                    $quizAnswers = QuizAnswers::where('question_id', $questionId)->get();

                    $reqValue = $req->input('trueFalse-' . $questionId);

                    // First set all answers for this question to not correct
                    QuizAnswers::where('question_id', $questionId)->update(['is_correct' => 0]);

                    // Then set the selected answer as correct
                    if (is_numeric($reqValue)) {
                        // If it's an ID (numeric value)
                        QuizAnswers::where('id', $reqValue)
                                ->update(['is_correct' => 1]);
                    } else {
                        // If it's answer text (string value)
                        QuizAnswers::where('question_id', $questionId)
                                 ->where('answer', $reqValue)
                                 ->update(['is_correct' => 1]);
                    }
                }
            }
        }

        return redirect(route('allBooksDashboard'))->withSuccess('Book Updated Successfully');
    }

    public function getAdminQuizLayers()
    {
        $quizResponses = QuizUserRespnose::with('user', 'book', 'question', 'question.answers')->get();
        $quizs = $quizResponses->groupBy('book_id');

        return view('admin.view.quiz', compact('quizs'));
    }

    public function getAdminQuizDetails($bookId){
        $QuizBooks = QuizUserRespnose::where('book_id', $bookId)->with('user', 'book', 'question', 'question.answers')->get();
        return view('admin.view.quiz-details', compact('QuizBooks'));
    }
}
