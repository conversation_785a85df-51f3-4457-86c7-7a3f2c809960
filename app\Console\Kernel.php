<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Artisan;
use App\Models\SystemControl;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\CheckPendingReviews::class,
        Commands\TestAmazonApiConnection::class,
        Commands\AutoCheckReviews::class,
        Commands\RejectOverdueReviews::class,
        Commands\RunAutocheckNow::class,
        Commands\CheckProductionFlow::class,
        Commands\FixSubscriptionPlanTypes::class,
        Commands\ResetWeeklyLogins::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule): void
    {
        // Schedule daily tasks
        $schedule->command('system:reset-credits')->daily();
        $schedule->command('system:checkSoldBooks')->daily();
        $schedule->command('system:checkPendingCancellation')->daily();

        // Schedule the auto-check reviews command directly instead of using a callback
        // This ensures it will run reliably every minute and check if it needs to process reviews
        $schedule->command('system:auto-check-reviews')->everyMinute();

        // Schedule the reject overdue reviews command
        $schedule->command('system:reject-overdue-reviews')->daily();

        // Schedule the production flow check command to run every minute
        // This ensures the system will switch to Production mode when the countdown expires
        $schedule->command('system:check-production-flow')->everyMinute();

        // Schedule the fix subscription plan types command to run daily
        // This ensures all subscription plan types are correct
        $schedule->command('subscriptions:fix-plan-types')->daily();

        // Schedule the weekly login reset command
        $schedule->command('reset:weekly-logins')->weekly(); // Runs every Sunday at midnight
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
