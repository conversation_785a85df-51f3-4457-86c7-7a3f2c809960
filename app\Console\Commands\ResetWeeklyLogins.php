<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class ResetWeeklyLogins extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reset:weekly-logins';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Resets weekly login counts for all users.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        User::query()->update([
            'weekly_logins' => 0,
            'last_login_week' => null, // Reset this to ensure it's re-evaluated on next login
        ]);

        Log::info('Weekly login counts reset for all users.');

        $this->info('Weekly login counts reset successfully!');

        return Command::SUCCESS;
    }
}
