<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'amazon' => [
        'api_key' => env('AMAZON_API_KEY'),
        'api_secret' => env('AMAZON_API_SECRET'),
        'partner_tag' => env('AMAZON_PARTNER_TAG'),
        'region' => env('AMAZON_REGION', 'us'),
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_REDIRECT_URI', '/auth/google/callback'),
    ],

    'paddle' => [
        'api_key' => env('PADDLE_API_KEY', '1973411c65a3215820a203c320134f86467d6ceab097c20c0c'),
        'client_token' => env('PADDLE_CLIENT_TOKEN', 'test_d9d44c3563808d7ff455dc03177'),
        'vendor_id' => env('PADDLE_VENDOR_ID', '195322'),
        'sandbox' => env('PADDLE_SANDBOX', true),
        'products' => [
            'auteur' => env('PADDLE_PRODUCT_AUTEUR', 'pro_01jrny6hs48a2m1t866kb6tcj4'),
            'auteur_plus' => env('PADDLE_PRODUCT_AUTEUR_PLUS', 'pro_01jr2y1s6eze5npfgx2wzggh3w'),
            'early_bird' => env('PADDLE_PRODUCT_EARLY_BIRD', 'pro_01jrnycy88eb57k74pa122dpgs'),
            'auteur_core' => env('PADDLE_PRODUCT_AUTEUR_CORE', 'pro_01jvd11k90te2455cae25yeqx4'),
        ],
        'prices' => [
            'auteur' => env('PADDLE_PRICE_AUTEUR', 'pri_01jrny94jqrzqkcpnq99vyaeb0'),
            'auteur_plus' => env('PADDLE_PRICE_AUTEUR_PLUS', 'pri_01jr2y6h3ds7549rw733nn1a4g'),
            'early_bird' => env('PADDLE_PRICE_EARLY_BIRD', 'pri_01jrnye618smhkvfydqfdzh97y'),
            'auteur_core' => env('PADDLE_PRICE_AUTEUR_CORE', 'pri_01jvd13h5k8rq1a6ew9b3yepdh'),
        ],
    ],

];
