<?php

namespace App\Services;

use App\Models\SystemControl;
use App\Models\User;
use App\Models\Subscription;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class SubscriptionFlowService
{
    /**
     * Get the current subscription flow state
     *
     * @return string 'alpha' or 'production'
     */
    public static function getCurrentFlow()
    {
        return Cache::remember('subscription_flow', 60, function () {
            $flowControl = SystemControl::where('key_type', 'subscription_flow')->first();

            if (!$flowControl) {
                // Default to alpha if not set
                return 'alpha';
            }

            // Convert integer value to string representation
            // 0 = alpha, 1 = production
            return (int)$flowControl->value === 1 ? 'production' : 'alpha';
        });
    }

    /**
     * Non-static version of getCurrentFlow for backward compatibility
     *
     * @return string 'alpha' or 'production'
     */
    public function getFlow()
    {
        return self::getCurrentFlow();
    }

    /**
     * Check if the system is in Alpha flow
     *
     * @return bool
     */
    public function isAlphaFlow()
    {
        return $this->getFlow() === 'alpha';
    }

    /**
     * Check if the system is in Production flow
     *
     * @return bool
     */
    public function isProductionFlow()
    {
        return $this->getFlow() === 'production';
    }

    /**
     * Get the production switch time
     *
     * @return int|null Unix timestamp or null if not set
     */
    public function getProductionSwitchTime()
    {
        $switchTimeControl = SystemControl::where('key_type', 'production_switch_time')->first();

        if (!$switchTimeControl || (int)$switchTimeControl->value === 0) {
            return null;
        }

        return (int) $switchTimeControl->value;
    }

    /**
     * Check if the production countdown has started
     *
     * @return bool
     */
    public function isProductionCountdownStarted()
    {
        return $this->getProductionSwitchTime() !== null;
    }

    /**
     * Check if the production countdown has expired
     *
     * @return bool
     */
    public function isProductionCountdownExpired()
    {
        $switchTime = $this->getProductionSwitchTime();

        if ($switchTime === null) {
            return false;
        }

        return time() >= $switchTime;
    }

    /**
     * Get the remaining time until production switch in seconds
     *
     * @return int|null Seconds remaining or null if countdown not started
     */
    public function getProductionCountdownRemainingSeconds()
    {
        $switchTime = $this->getProductionSwitchTime();

        if ($switchTime === null) {
            return null;
        }

        $remaining = $switchTime - time();
        return $remaining > 0 ? $remaining : 0;
    }

    /**
     * Get the remaining days until production switch
     *
     * @return string Formatted string with days remaining
     */
    public function getProductionCountdownRemainingDays()
    {
        $remainingSeconds = $this->getProductionCountdownRemainingSeconds();

        if ($remainingSeconds === null) {
            return "soon"; // Default fallback if no countdown is set
        }

        // Convert seconds to hours
        $remainingHours = $remainingSeconds / 3600;

        // If less than 24 hours, show "in 1 day"
        if ($remainingHours < 24) {
            return "in 1 day";
        }

        // Otherwise, convert to days and round up to the nearest whole day
        $remainingDays = ceil($remainingHours / 24);
        return "in {$remainingDays} days";
    }

    /**
     * Start the production countdown
     *
     * @param int $hours Number of hours until production switch
     * @return bool
     */
    public function startProductionCountdown($hours = 48)
    {
        try {
            // Calculate the switch time
            $switchTime = time() + ($hours * 3600);

            // Update the production_switch_time system control
            $switchTimeControl = SystemControl::where('key_type', 'production_switch_time')->first();

            if ($switchTimeControl) {
                $switchTimeControl->value = $switchTime;
                $switchTimeControl->save();
            } else {
                SystemControl::create([
                    'key' => 'Production Switch Time',
                    'key_type' => 'production_switch_time',
                    'value' => $switchTime
                ]);
            }

            // Update the production_countdown_hours system control
            $countdownHoursControl = SystemControl::where('key_type', 'production_countdown_hours')->first();

            if ($countdownHoursControl) {
                $countdownHoursControl->value = $hours;
                $countdownHoursControl->save();
            } else {
                SystemControl::create([
                    'key' => 'Production Countdown Hours',
                    'key_type' => 'production_countdown_hours',
                    'value' => $hours
                ]);
            }

            Log::info('Production countdown started', [
                'hours' => $hours,
                'switch_time' => $switchTime,
                'switch_time_formatted' => date('Y-m-d H:i:s', $switchTime)
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to start production countdown', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Switch to Production flow
     *
     * @return bool
     */
    public function switchToProductionFlow()
    {
        try {
            // Update the subscription_flow system control
            $flowControl = SystemControl::where('key_type', 'subscription_flow')->first();

            if ($flowControl) {
                $flowControl->value = 1; // 1 = production
                $flowControl->save();
            } else {
                SystemControl::create([
                    'key' => 'Subscription Flow',
                    'key_type' => 'subscription_flow',
                    'value' => 1 // 1 = production
                ]);
            }

            Log::info('Switched to Production flow');

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to switch to Production flow', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Switch back to Alpha flow (for testing purposes)
     *
     * @return bool
     */
    public function switchToAlphaFlow()
    {
        try {
            // Update the subscription_flow system control
            $flowControl = SystemControl::where('key_type', 'subscription_flow')->first();

            if ($flowControl) {
                $flowControl->value = 0; // 0 = alpha
                $flowControl->save();
            } else {
                SystemControl::create([
                    'key' => 'Subscription Flow',
                    'key_type' => 'subscription_flow',
                    'value' => 0 // 0 = alpha
                ]);
            }

            // Reset the production switch time
            $switchTimeControl = SystemControl::where('key_type', 'production_switch_time')->first();

            if ($switchTimeControl) {
                $switchTimeControl->value = 0;
                $switchTimeControl->save();
            }

            Log::info('Switched back to Alpha flow');

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to switch to Alpha flow', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Check if a user should see the production warning message
     *
     * @param User $user
     * @return bool
     */
    public function shouldShowProductionWarning(User $user)
    {
        // Log the current state for debugging
        Log::info('Checking if user should see production warning', [
            'user_id' => $user->id,
            'current_flow' => $this->getCurrentFlow(),
            'countdown_started' => $this->isProductionCountdownStarted(),
            'countdown_expired' => $this->isProductionCountdownExpired(),
            'remaining_seconds' => $this->getProductionCountdownRemainingSeconds()
        ]);

        // Only show warning to users with Alpha subscriptions
        $subscription = $user->activeSubscription();
        if (!$subscription || !$subscription->isAlpha()) {
            return false;
        }

        // In Production flow, always show warning to Alpha users until they upgrade
        if ($this->isProductionFlow()) {
            Log::info('Alpha user in Production mode should see upgrade warning', [
                'user_id' => $user->id,
                'subscription_type' => $subscription->plan_type
            ]);
            return true;
        }

        // In Alpha flow, only show warning if countdown has started but not expired
        if ($this->isAlphaFlow() && $this->isProductionCountdownStarted() && !$this->isProductionCountdownExpired()) {
            Log::info('Alpha user in Alpha mode with active countdown should see warning', [
                'user_id' => $user->id,
                'countdown_remaining' => $this->getProductionCountdownRemainingSeconds()
            ]);
            return true;
        }

        return false;
    }

    /**
     * Check if a user should be blocked from the vault
     *
     * @param User $user
     * @return bool
     */
    public function shouldBlockFromVault(User $user)
    {
        // Log the current state for debugging
        Log::info('Checking if user should be blocked from vault', [
            'user_id' => $user->id,
            'current_flow' => $this->getCurrentFlow(),
            'is_production_flow' => $this->isProductionFlow(),
            'countdown_expired' => $this->isProductionCountdownExpired(),
            'remaining_seconds' => $this->getProductionCountdownRemainingSeconds()
        ]);

        // Only block if in production flow and countdown has expired
        if (!$this->isProductionFlow()) {
            Log::info('Not blocking user from vault - not in production flow', ['user_id' => $user->id]);
            return false;
        }

        if (!$this->isProductionCountdownExpired()) {
            Log::info('Not blocking user from vault - countdown not expired', ['user_id' => $user->id]);
            return false;
        }

        // Only block users with Alpha subscriptions
        $subscription = $user->activeSubscription();
        if (!$subscription) {
            Log::info('Not blocking user from vault - no active subscription', ['user_id' => $user->id]);
            return false;
        }

        if (!$subscription->isAlpha()) {
            Log::info('Not blocking user from vault - not an Alpha subscription', [
                'user_id' => $user->id,
                'plan_type' => $subscription->plan_type
            ]);
            return false;
        }

        Log::info('Blocking Alpha user from vault in Production mode', ['user_id' => $user->id]);
        return true;
    }

    /**
     * Create an Alpha subscription for a user
     *
     * @param User $user
     * @return Subscription
     */
    public function createAlphaSubscription(User $user)
    {
        // CRITICAL FIX: Set product_id to 'alpha' explicitly for Alpha subscriptions
        // This ensures the plan_type is correctly identified in the PaddleService
        return Subscription::create([
            'user_id' => $user->id,
            'product_id' => 'alpha', // Explicitly set to 'alpha' instead of using Auteur product ID
            'plan_type' => 'alpha',
            'status' => 'active',
            'amount' => 0.00,
            'currency' => 'USD',
            'billing_period_start' => now(),
            'billing_period_end' => now()->addYears(10) // Effectively unlimited
        ]);
    }
}
