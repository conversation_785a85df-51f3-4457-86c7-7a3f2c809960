@extends('master-layout.master-layout')

@section('page_title', 'User Profile')
@section('page_content')
<div class="container py-5">
    <div class="row">
        <!-- Left sidebar navigation -->
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header">User Profile</div>
                <div class="list-group list-group-flush">
                    <a href="#profile-section" class="list-group-item list-group-item-action active">Profile</a>
                    <a href="#security-section" class="list-group-item list-group-item-action">Security</a>
                </div>
            </div>

            <div class="card">
                <div class="card-header">Billing</div>
                <div class="list-group list-group-flush">
                    <a href="#subscription-section" class="list-group-item list-group-item-action">Subscription</a>
                    <a href="#payment-method-section" class="list-group-item list-group-item-action">Payment Method</a>
                    <a href="#invoices-section" class="list-group-item list-group-item-action">Invoices History</a>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <div class="col-md-9">
            <!-- Flash messages are now handled by master layout -->

            <!-- Profile Information Section -->
            <div class="card mb-4" id="profile-section">
                 <div class="card-header">
                    <h1 class="CreateBookFields">Profile</h1>
                </div>
                <div class="card-body">
                    <!-- Contact Information Section -->
                    <div class="mb-4">
                        <h3 class="CreateBookFields">Contact Information</h3>
                        <form action="{{ route('profile.update') }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="fullName" class="BookFieldsExplain">Name</label>
                                <div class="d-flex align-items-center">
                                    <input type="text" class="form-control @error('fullName') is-invalid @enderror" id="fullName" name="fullName" value="{{ old('fullName', $user->fullName) }}" readonly style="color: #6c757d; background-color: #e9ecef;">
                                    @if($user->is_core_auteur_candidate)
                                        <span class="badge bg-primary ms-2" title="Core Auteur">Core Auteur</span>
                                    @endif
                                </div>
                                @error('fullName')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label for="email" class="BookFieldsExplain">E-Mail Address</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', $user->email) }}">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">Update Contact Info</button>
                            </div>
                        </form>
                    </div>

                    <!-- Amazon Reviewer Name Section -->
                    <div>
                        <h3 class="CreateBookFields">Your Amazon Reviewer Name</h3>
                        <form action="{{ route('profile.amazon_reviewer') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-8">
                                    <label for="amazon_reviewer_name" class="BookFieldsExplain">
                                        Amazon Reviewer Name {{ $updateInterval > 0 ? "(can be updated once per {$updateInterval} days)" : "" }}
                                    </label>
                                    <input type="text" class="form-control @error('amazon_reviewer_name') is-invalid @enderror" id="amazon_reviewer_name" name="amazon_reviewer_name" value="{{ old('amazon_reviewer_name', $user->amazon_reviewer_name) }}">
                                    @if($daysRemaining > 0)
                                        <div class="text-danger mt-1">
                                            Please wait {{ $daysRemaining }} day(s) until Your Amazon Reviewer Name can be updated
                                        </div>
                                    @endif
                                    @error('amazon_reviewer_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="text-end mt-3">
                                <button type="submit" class="btn btn-primary">Update Amazon Reviewer Name</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Security Section -->
            <div class="card" id="security-section">
                <div class="card-header">
                    <h3 class="CreateBookFields">Security</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('profile.password') }}" method="POST">
                        @csrf
                        <div class="mb-3">
                            <label for="current_password" class="BookFieldsExplain">Current Password</label>
                            <input type="password" class="form-control @error('current_password') is-invalid @enderror" id="current_password" name="current_password">
                            @error('current_password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="BookFieldsExplain">New Password</label>
                            <input type="password" class="form-control @error('new_password') is-invalid @enderror" id="new_password" name="new_password">
                            @error('new_password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="new_password_confirmation" class="BookFieldsExplain">Confirm New Password</label>
                            <input type="password" class="form-control @error('new_password_confirmation') is-invalid @enderror" id="new_password_confirmation" name="new_password_confirmation">
                            @error('new_password_confirmation')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Update Password</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Subscription Section -->
            <div class="card mb-4" id="subscription-section">
                <div class="card-header">
                    <h3 class="CreateBookFields">Subscription</h3>
                </div>
                <div class="card-body">
                    @if($subscription)
                        <div class="mb-4">
                            <h6 class="BookFieldsExplain">Current Plan:</h6>
                            <div class="p-3 bg-light rounded">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        @if($subscription->isAlpha())
                                            <h5 class="mb-0">AUTEUR Alpha</h5>
                                            <p class="text-muted mb-0">$0 / Monthly (Free in Alpha)</p>
                                        @elseif($subscription->isEarlyBird())
                                            <h5 class="mb-0">AUTEUR Early Bird</h5>
                                            <p class="text-muted mb-0">$9 / Monthly</p>
                                        @elseif($subscription->isAuteurPlus())
                                            <h5 class="mb-0">AUTEUR+</h5>
                                            <p class="text-muted mb-0">$240 / Yearly ($10 per month)</p>
                                        @else
                                            <h5 class="mb-0">AUTEUR</h5>
                                            <p class="text-muted mb-0">$15 / Monthly</p>
                                        @endif
                                    </div>
                                    <span class="badge {{ $subscription->status == 'active' ? 'bg-success' : 'bg-warning text-dark' }}">
                                        {{ ucfirst($subscription->status) }}
                                    </span>
                                </div>

                                <div class="mt-3">
                                    <h6>Plan Features:</h6>
                                    <ul class="list-unstyled">
                                        @if($subscription->isAuteurPlus())
                                            <li><i class="ri-check-line text-success me-2"></i>Add unlimited books</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Get up to 7 reviews per week</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Advanced analytics</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Priority support</li>
                                        @elseif($subscription->isEarlyBird())
                                            <li><i class="ri-check-line text-success me-2"></i>Add up to 3 books</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Get up to 5 reviews per week</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Full vault access</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Basic analytics</li>
                                        @elseif($subscription->isAlpha())
                                            <li><i class="ri-check-line text-success me-2"></i>Add up to 3 books</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Get up to 3 reviews per week</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Basic analytics</li>
                                            @if(isset($vaultRestricted) && $vaultRestricted)
                                                <li><i class="ri-close-line text-danger me-2"></i>Vault access restricted</li>
                                            @endif
                                        @else
                                            <li><i class="ri-check-line text-success me-2"></i>Add up to 3 books</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Get up to 5 reviews per week</li>
                                            <li><i class="ri-check-line text-success me-2"></i>Basic analytics</li>
                                        @endif
                                    </ul>
                                </div>

                                @if($subscription->billing_period_end)
                                    <div class="mt-3">
                                        <p class="mb-0"><strong>Next billing date:</strong> {{ $subscription->billing_period_end->format('F j, Y') }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="d-flex mt-4">
                            @if($showCoreAuteurUpgrade)
                                <div class="alert alert-info text-center mb-4" style="background-color: #e0f7fa; border-color: #b2ebf2; color: #007bff;">
                                    <p class="mb-2">Special Offer: Upgrade to AUTEUR Core!</p>
                                    <p class="mb-3">1-year Premium membership for FREE! ($0 Per Month For The 1st Year; then - $13.00/month)</p>
                                    <a href="{{ route('subscription.direct-checkout', 'auteur_core') }}" class="btn btn-primary">Upgrade to AUTEUR Core</a>
                                </div>
                            @endif

                            @if($subscription->isAlpha())
                                @if(isset($productionFlow) && $productionFlow === 'production')
                                    <a href="{{ route('subscription.direct-checkout', 'early_bird') }}" class="btn btn-primary me-2">Upgrade to Early Bird Plan</a>
                                @else
                                    <button class="btn btn-secondary me-2" disabled>Upgrade your Plan</button>
                                    <small class="text-muted mt-2">Upgrades will be available in Production mode</small>
                                @endif
                            @elseif($subscription->isEarlyBird())
                                @if(isset($productionFlow) && $productionFlow === 'production')
                                    <a href="{{ route('subscription.direct-checkout', 'auteur_plus') }}" class="btn btn-primary me-2">Upgrade to AUTEUR+</a>
                                @else
                                    <button class="btn btn-secondary me-2" disabled>AUTEUR+ Available in Production</button>
                                @endif
                            @elseif(!$subscription->isAuteurPlus())
                                @if(isset($productionFlow) && $productionFlow === 'production')
                                    <a href="{{ route('subscription.direct-checkout', 'auteur_plus') }}" class="btn btn-primary me-2">Upgrade to AUTEUR+</a>
                                @else
                                    <button class="btn btn-secondary me-2" disabled>AUTEUR+ Available in Production</button>
                                @endif
                            @endif

                            @if($subscription->status == 'active' && !$subscription->isAlpha())
                                <div class="d-flex">
                                    <form action="{{ route('subscription.cancel') }}" method="POST">
                                        @csrf
                                        <button type="submit" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to cancel your subscription?')">
                                            Cancel Subscription
                                        </button>
                                    </form>
                                </div>
                            @elseif($subscription->status == 'canceled' || $subscription->status == 'cancellation_requested')
                                <div class="d-flex">
                                    <a href="{{ route('subscription.plans') }}" class="btn btn-primary">Reactivate Subscription</a>
                                </div>
                            @endif
                        </div>
                    @elseif($hasAnySubscription)
                        <div class="text-center p-4 bg-light rounded mb-4">
                            <p class="mb-4">Your subscription has been canceled.</p>
                            <div class="mt-4 d-flex justify-content-center">
                                <a href="{{ route('subscription.direct-checkout', 'auteur') }}" class="btn btn-primary">Reactivate Subscription</a>
                            </div>
                        </div>
                    @else
                        <div class="text-center p-4 bg-light rounded mb-4">
                            <p class="mb-4">You don't have an active subscription.</p>
                            <div class="mt-4">
                                <a href="{{ route('subscription.direct-checkout', 'auteur') }}" class="btn btn-primary me-2">Subscribe to AUTEUR</a>
                                @if(isset($productionFlow) && $productionFlow === 'production')
                                    <a href="{{ route('subscription.direct-checkout', 'auteur_plus') }}" class="btn btn-success">Subscribe to AUTEUR+</a>
                                @else
                                    <button class="btn btn-secondary" disabled>AUTEUR+ Available in Production</button>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Payment Method Section -->
            <div class="card mb-4" id="payment-method-section">
                <div class="card-header">
                    <h3 class="CreateBookFields">Payment Method</h3>
                </div>
                <div class="card-body">
                    @if($subscription && $subscription->status == 'active')
                        <div class="mb-4">
                            <h6 class="BookFieldsExplain">Current Payment Method:</h6>
                            <div class="p-3 bg-light rounded">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="ri-bank-card-line" style="font-size: 2rem;"></i>
                                    </div>
                                    <div>
                                        <p class="mb-0">•••• •••• •••• 4242</p>
                                        <p class="text-muted mb-0">Expires: 12/2025</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <form id="update-payment-form">
                                <div class="mb-3">
                                    <label for="cardholderName" class="BookFieldsExplain">Cardholder's Name</label>
                                    <input type="text" class="form-control" id="cardholderName" name="cardholderName">
                                </div>
                                <div class="mb-3">
                                    <label for="cardNumber" class="BookFieldsExplain">Card</label>
                                    <input type="text" class="form-control" id="cardNumber" name="cardNumber" placeholder="•••• •••• •••• ••••">
                                </div>
                                <div class="mb-3">
                                    <label for="zipCode" class="BookFieldsExplain">ZIP / Postal Code</label>
                                    <input type="text" class="form-control" id="zipCode" name="zipCode">
                                </div>
                                <button type="button" id="update-payment-button" class="btn btn-primary" style="background-color: #3f51b5; border-color: #3f51b5;">
                                    Update Payment Method
                                </button>
                            </form>
                            <p class="text-muted mt-2"><small>Your payment information is securely processed by Paddle.</small></p>
                            @if(config('services.paddle.sandbox', true))
                            <div class="alert alert-info mt-3">
                                <i class="ri-information-line me-2"></i>
                                <strong>Sandbox Mode</strong>
                                <p class="mb-0 mt-1">In sandbox mode, payment method updates have limited functionality. This will work correctly in production.</p>
                            </div>
                            @endif
                        </div>

                        <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
                        <script>
                            // Clear any form fields on page load
                            document.addEventListener('DOMContentLoaded', function() {
                                const formFields = document.querySelectorAll('#update-payment-form input');
                                formFields.forEach(field => {
                                    field.value = '';
                                });
                            });

                            document.getElementById('update-payment-button').addEventListener('click', function() {
                                // Get the subscription ID
                                const subscriptionId = '{{ $subscription->paddle_subscription_id ?? "" }}';

                                if (!subscriptionId) {
                                    showErrorModal('Unable to update payment method. Please contact support.');
                                    return;
                                }

                                // Initialize Paddle
                                Paddle.Environment.set('sandbox');
                                Paddle.Setup({
                                    token: '{{ config("services.paddle.client_token") }}',
                                    eventCallback: function(event) {
                                        console.log('Paddle event:', event.name, event);
                                    }
                                });

                                // Make a request to get a transaction for updating payment method
                                fetch('/subscription/update-payment/' + subscriptionId)
                                    .then(response => response.json())
                                    .then(data => {
                                        // Check for sandbox limitation
                                        if (data.sandbox_limitation) {
                                            // Show a more informative message for sandbox limitations
                                            showSandboxLimitationModal(data.message || 'Payment method updates are limited in sandbox mode.');
                                            return;
                                        }

                                        if (data.success && data.transaction_id) {
                                            // Open Paddle Checkout with the transaction ID
                                            Paddle.Checkout.open({
                                                transactionId: data.transaction_id,
                                                settings: {
                                                    displayMode: 'overlay',
                                                    theme: 'light',
                                                    locale: 'en',
                                                    successUrl: window.location.href
                                                }
                                            });
                                        } else {
                                            throw new Error(data.message || 'Unable to update payment method');
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error:', error);
                                        showErrorModal('Unable to update payment method. Please contact support.');
                                    });
                            });

                            function showErrorModal(message) {
                                // Show custom modal for error
                                const modalHtml = `
                                    <div class="modal fade" id="paymentErrorModal" tabindex="-1" aria-labelledby="paymentErrorModalLabel" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="paymentErrorModalLabel">localhost:8000</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>${message}</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal" style="background-color: #17a2b8; border-color: #17a2b8;">OK</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `;

                                // Add modal to the document
                                const modalContainer = document.createElement('div');
                                modalContainer.innerHTML = modalHtml;
                                document.body.appendChild(modalContainer.firstElementChild);

                                // Show the modal
                                const modal = new bootstrap.Modal(document.getElementById('paymentErrorModal'));
                                modal.show();
                            }

                            function showSandboxLimitationModal(message) {
                                // Show custom modal for sandbox limitations
                                const modalHtml = `
                                    <div class="modal fade" id="sandboxLimitationModal" tabindex="-1" aria-labelledby="sandboxLimitationModalLabel" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="sandboxLimitationModalLabel">Sandbox Mode Limitation</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="alert alert-info mb-3">
                                                        <i class="ri-information-line me-2"></i>
                                                        <strong>Sandbox Mode</strong>
                                                    </div>
                                                    <p>${message}</p>
                                                    <p class="mt-2">In the Paddle sandbox environment, payment method updates are not fully supported. This functionality will work correctly in production mode.</p>
                                                    <p class="mt-2">For testing purposes, you can use the Customer Portal links in the Paddle dashboard to manage subscriptions.</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `;

                                // Add modal to the document
                                const modalContainer = document.createElement('div');
                                modalContainer.innerHTML = modalHtml;
                                document.body.appendChild(modalContainer.firstElementChild);

                                // Show the modal
                                const modal = new bootstrap.Modal(document.getElementById('sandboxLimitationModal'));
                                modal.show();
                            }
                        </script>
                    @else
                        <div class="text-center p-4 bg-light rounded">
                            <p>No payment method on file.</p>
                            <p class="text-muted">A payment method will be added when you subscribe to a plan.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Invoices History Section -->
            <div class="card mb-4" id="invoices-section">
                <div class="card-header">
                    <h3 class="CreateBookFields">Invoices History</h3>
                </div>
                <div class="card-body">
                    @if($subscription)
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Description</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>{{ $subscription->created_at->format('M d, Y') }}</td>
                                        <td>
                                            @if($isAuteurPlus)
                                                Auteur+ Subscription
                                            @elseif($isEarlyBird)
                                                Auteur Early Bird Subscription
                                            @else
                                                Auteur Subscription
                                            @endif
                                        </td>
                                        <td>
                                            @if($isAuteurPlus)
                                                $13.00
                                            @elseif($isEarlyBird)
                                                $9.00
                                            @else
                                                $0.00
                                            @endif
                                        </td>
                                        <td><span class="badge bg-success">Paid</span></td>
                                        <td><a href="#" class="btn btn-sm btn-outline-secondary disabled">View</a></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center p-4 bg-light rounded">
                            <p>No invoices available.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add smooth scrolling
        document.querySelectorAll('.list-group-item').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all links
                document.querySelectorAll('.list-group-item').forEach(item => {
                    item.classList.remove('active');
                });

                // Add active class to clicked link
                this.classList.add('active');

                // Smooth scroll to section
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                window.scrollTo({
                    top: targetElement.offsetTop - 20,
                    behavior: 'smooth'
                });
            });
        });
    });
</script>
@endsection
